absl-py
aqtp
array-record
cloud-accelerator-diagnostics
cloud-tpu-diagnostics
datasets>=4.0.0
flax>=0.11.0
gcsfs
google-api-python-client
google-cloud-aiplatform==1.61.0
google-cloud-monitoring
google-jetstream@git+https://github.com/AI-Hypercomputer/JetStream.git
grain[parquet]>=0.2.6
huggingface_hub
jax>=0.4.30
jaxlib>=0.4.30
jaxtyping
jsonlines
ml-collections
ml-goodput-measurement
mlperf-logging@git+https://github.com/mlperf/logging.git
numpy
omegaconf
optax
orbax-checkpoint>=0.11.22
pathwaysutils>=0.1.1
pillow>=11.1.0
pre-commit
protobuf==3.20.3
pyink
pylint
pytest
pytype
qwix@git+https://github.com/google/qwix.git
sentencepiece==0.2.0
tensorboard-plugin-profile
tensorboardx>=*******
tensorflow-datasets
tensorflow-text>=2.17.0
tensorflow>=2.16.0
tiktoken
transformers
tunix@git+https://github.com/google/tunix.git
pathwaysutils@git+https://github.com/AI-Hypercomputer/pathways-utils.git#5756f63
