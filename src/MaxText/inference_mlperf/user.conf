# The format of this config file is 'key = value'.
# The key has the format 'model.scenario.key'. Value is mostly int64_t.
# Model maybe '*' as wildcard. In that case the value applies to all models.
# All times are in milli seconds

# Set performance_sample_count for each model.
llama2-70b.*.performance_sample_count_override = 24576
mixtral-8x7b.*.performance_sample_count_override = 15000
*.Offline.min_duration = 600000


# In Offline scenario, we always have one query. But LoadGen maps this to
# min_sample_count internally in Offline scenario. If the dataset size is larger
# than 24576 we limit the min_query_count to 24576 and otherwise we use
# the dataset size as the limit
llama2-70b.Offline.min_query_count = 24576
mixtral-8x7b.Offline.min_query_count = 15000

# These fields should be defined and overridden by user.conf.
*.Offline.target_qps = 5.0
