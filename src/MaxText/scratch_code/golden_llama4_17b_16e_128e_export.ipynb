{"cells": [{"cell_type": "code", "execution_count": null, "id": "e257acf1", "metadata": {}, "outputs": [], "source": ["# This script will generate the golden data for Llama-4-Scout-17B-16E and Llama-4-Maverick-17B-128E\n", "# which can be used for logit verificaiton / testing\n", "# NOTE: to change the model size, change the MODEL_SIZE variable in cell 3"]}, {"cell_type": "code", "execution_count": null, "id": "0d13ebbb", "metadata": {}, "outputs": [], "source": ["!python3 -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu\n", "!python3 -m pip install tokenizers -U\n", "!python3 -m pip install transformers -U"]}, {"cell_type": "code", "execution_count": null, "id": "6a8a4bb6", "metadata": {}, "outputs": [], "source": ["import torch\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import jsonlines\n", "\n", "MODEL_SIZE = \"scout\" # \"scout\" or \"maverick\"\n", "assert MODEL_SIZE in [\"scout\", \"maverick\"]"]}, {"cell_type": "code", "execution_count": null, "id": "ff804403", "metadata": {}, "outputs": [], "source": ["# Load the tokenizer and model from Hugging Face\n", "\n", "model_id = \"meta-llama/Llama-4-Scout-17B-16E\" if MODEL_SIZE == \"scout\" else \"meta-llama/Llama-4-Maverick-17B-128E\"\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(model_id)\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    model_id,\n", "    trust_remote_code=True,\n", "    torch_dtype=\"float32\",\n", ")\n", "\n", "# Save to disk\n", "model_size_to_num_experts = \"16e\" if MODEL_SIZE == \"scout\" else \"128e\"\n", "output_path = f\"golden_data_llama4-17b-{model_size_to_num_experts}.jsonl\"\n", "\n", "\n", "# Your prompt text\n", "prompt_texts = [\"I love to\"]\n", "all_data_to_save = []\n", "\n", "\n", "for prompt_text in prompt_texts:\n", "  # Encode the prompt text\n", "  input_ids = tokenizer.encode(prompt_text, return_tensors=\"pt\")\n", "  print(f\"Input ids are {input_ids}\")\n", "\n", "  # Get the logits for the prompt + completion\n", "  with torch.no_grad():\n", "    # NOTE: `use_cache=False` is needed, otherwise you'll get an error complaining about mixing\n", "    # BF16 and FP32\n", "    outputs = model(input_ids, use_cache=False)\n", "    logits = outputs.logits\n", "\n", "    # Convert logits to fp32\n", "    logits = logits.cpu().numpy().astype(\"float32\")\n", "\n", "    # Prepare data to be saved\n", "    data_to_save = {\n", "        \"prompt\": prompt_text,\n", "        \"tokens\": input_ids.tolist()[0], # squeeze batch (of 1) out\n", "        \"logits\": logits.tolist()[0],  # # squeeze batch (of 1) out + convert numpy array to list for JSON serialization\n", "    }\n", "    all_data_to_save.append(data_to_save)\n", "\n", "with jsonlines.open(output_path, \"w\") as f:\n", "  f.write_all(all_data_to_save)\n", "\n", "\n", "print(f\"Data saved to {output_path}\")"]}], "metadata": {"kernelspec": {"display_name": "llama4-src/MaxText", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}