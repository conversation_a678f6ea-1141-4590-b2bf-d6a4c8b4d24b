{"cells": [{"cell_type": "code", "execution_count": null, "id": "0d13ebbb", "metadata": {}, "outputs": [], "source": ["!python3 -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu\n", "!python3 -m pip install tokenizers -U\n", "!python3 -m pip install transformers -U"]}, {"cell_type": "code", "execution_count": 1, "id": "6a8a4bb6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/devel/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import torch\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import jsonlines"]}, {"cell_type": "code", "execution_count": 2, "id": "ff804403", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|██████████| 59/59 [03:54<00:00,  3.97s/it]\n"]}], "source": ["# Load the tokenizer and model from Hugging Face\n", "\n", "model_id = \"mistralai/Mixtral-8x22B-Instruct-v0.1\"\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(model_id)\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    model_id,\n", "    torch_dtype=torch.float16,\n", ")"]}, {"cell_type": "markdown", "id": "9f218ba6", "metadata": {}, "source": ["## looping over multiple prompts and logits"]}, {"cell_type": "code", "execution_count": 3, "id": "c567f8d9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["We detected that you are passing `past_key_values` as a tuple and this is deprecated and will be removed in v4.43. Please use an appropriate `Cache` class (https://huggingface.co/docs/transformers/v4.41.3/en/internal/generation_utils#transformers.Cache)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Data saved to golden_data_mixtral-8x22b.jsonl\n"]}], "source": ["# Save to disk\n", "output_path = \"golden_data_mixtral-8x22b.jsonl\"\n", "\n", "\n", "# Your prompt text\n", "prompt_texts = [\"[INST] I love to [/INST]\", \"[INST] Today is a [/INST]\", \"[INST] What is the [/INST]\"]\n", "all_data_to_save = []\n", "\n", "\n", "for prompt_text in prompt_texts:\n", "  # Encode the prompt text\n", "  input_ids = tokenizer.encode(prompt_text, return_tensors=\"pt\")\n", "\n", "  # Get the logits for the prompt + completion\n", "  with torch.no_grad():\n", "    outputs = model(input_ids)\n", "    logits = outputs.logits\n", "\n", "    # Convert logits to fp32\n", "    logits = logits.cpu().numpy().astype(\"float32\")\n", "\n", "    # Prepare data to be saved\n", "    data_to_save = {\n", "        \"prompt\": prompt_text,\n", "        \"tokens\": input_ids.tolist()[0],\n", "        \"logits\": logits.tolist()[0],  # Convert numpy array to list for JSON serialization\n", "    }\n", "    all_data_to_save.append(data_to_save)\n", "\n", "with jsonlines.open(output_path, \"w\") as f:\n", "  f.write_all(all_data_to_save)\n", "\n", "\n", "print(f\"Data saved to {output_path}\")"]}, {"cell_type": "code", "execution_count": null, "id": "82c6e1f7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 5}