{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0d13ebbb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulting to user installation because normal site-packages is not writeable\n", "Looking in indexes: https://download.pytorch.org/whl/cpu\n", "Collecting torch\n", "  Downloading https://download.pytorch.org/whl/cpu/torch-2.4.0%2Bcpu-cp310-cp310-linux_x86_64.whl (195.0 MB)\n", "\u001b[2K     \u001b[38;2;114;156;31m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m195.0/195.0 MB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0mm eta \u001b[36m0:00:01\u001b[0m[36m0:00:01\u001b[0m\n", "\u001b[?25hCollecting torchvision\n", "  Downloading https://download.pytorch.org/whl/cpu/torchvision-0.19.0%2Bcpu-cp310-cp310-linux_x86_64.whl (1.6 MB)\n", "\u001b[2K     \u001b[38;2;114;156;31m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.6/1.6 MB\u001b[0m \u001b[31m81.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting torchaudio\n", "  Downloading https://download.pytorch.org/whl/cpu/torchaudio-2.4.0%2Bcpu-cp310-cp310-linux_x86_64.whl (1.7 MB)\n", "\u001b[2K     \u001b[38;2;114;156;31m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.7/1.7 MB\u001b[0m \u001b[31m57.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from torch) (3.12.0)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /home/<USER>/.local/lib/python3.10/site-packages (from torch) (4.12.2)\n", "Collecting sympy (from torch)\n", "  Downloading https://download.pytorch.org/whl/sympy-1.12-py3-none-any.whl (5.7 MB)\n", "\u001b[2K     \u001b[38;2;114;156;31m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.7/5.7 MB\u001b[0m \u001b[31m111.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0mm eta \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hRequirement already satisfied: networkx in /home/<USER>/.local/lib/python3.10/site-packages (from torch) (3.1)\n", "Requirement already satisfied: jinja2 in /home/<USER>/.local/lib/python3.10/site-packages (from torch) (3.1.4)\n", "Requirement already satisfied: fsspec in /home/<USER>/.local/lib/python3.10/site-packages (from torch) (2024.5.0)\n", "Requirement already satisfied: numpy in /home/<USER>/.local/lib/python3.10/site-packages (from torchvision) (1.26.4)\n", "Collecting pillow!=8.3.*,>=5.3.0 (from torchvision)\n", "  Downloading https://download.pytorch.org/whl/pillow-10.2.0-cp310-cp310-manylinux_2_28_x86_64.whl (4.5 MB)\n", "\u001b[2K     \u001b[38;2;114;156;31m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.5/4.5 MB\u001b[0m \u001b[31m98.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m0m eta \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hRequirement already satisfied: MarkupSafe>=2.0 in /home/<USER>/.local/lib/python3.10/site-packages (from jinja2->torch) (2.1.5)\n", "Collecting mpmath>=0.19 (from sympy->torch)\n", "  Downloading https://download.pytorch.org/whl/mpmath-1.3.0-py3-none-any.whl (536 kB)\n", "\u001b[2K     \u001b[38;2;114;156;31m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m536.2/536.2 kB\u001b[0m \u001b[31m48.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h\u001b[33mWARNING: Error parsing dependencies of distro-info: Invalid version: '1.1build1'\u001b[0m\u001b[33m\n", "\u001b[0m\u001b[33mWARNING: Error parsing dependencies of python-debian: Invalid version: '0.1.43ubuntu1'\u001b[0m\u001b[33m\n", "\u001b[0mInstalling collected packages: mpmath, sympy, pillow, torch, torchvision, torchaudio\n", "Successfully installed mpmath-1.3.0 pillow-10.2.0 sympy-1.12 torch-2.4.0+cpu torchaudio-2.4.0+cpu torchvision-0.19.0+cpu\n", "Defaulting to user installation because normal site-packages is not writeable\n", "Requirement already satisfied: tokenizers in /home/<USER>/.local/lib/python3.10/site-packages (0.19.1)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in /home/<USER>/.local/lib/python3.10/site-packages (from tokenizers) (0.24.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (3.12.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /home/<USER>/.local/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (2024.5.0)\n", "Requirement already satisfied: packaging>=20.9 in /home/<USER>/.local/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (24.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/lib/python3/dist-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (5.4.1)\n", "Requirement already satisfied: requests in /home/<USER>/.local/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (2.32.3)\n", "Requirement already satisfied: tqdm>=4.42.1 in /home/<USER>/.local/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (4.66.4)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /home/<USER>/.local/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->huggingface-hub<1.0,>=0.16.4->tokenizers) (3.1.0)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/lib/python3/dist-packages (from requests->huggingface-hub<1.0,>=0.16.4->tokenizers) (3.3)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/lib/python3/dist-packages (from requests->huggingface-hub<1.0,>=0.16.4->tokenizers) (1.26.5)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/.local/lib/python3.10/site-packages (from requests->huggingface-hub<1.0,>=0.16.4->tokenizers) (2024.7.4)\n", "\u001b[33mWARNING: Error parsing dependencies of distro-info: Invalid version: '1.1build1'\u001b[0m\u001b[33m\n", "\u001b[0m\u001b[33mWARNING: Error parsing dependencies of python-debian: Invalid version: '0.1.43ubuntu1'\u001b[0m\u001b[33m\n", "\u001b[0mDefaulting to user installation because normal site-packages is not writeable\n", "Requirement already satisfied: transformers in /home/<USER>/.local/lib/python3.10/site-packages (4.43.3)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from transformers) (3.12.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.23.2 in /home/<USER>/.local/lib/python3.10/site-packages (from transformers) (0.24.2)\n", "Requirement already satisfied: numpy>=1.17 in /home/<USER>/.local/lib/python3.10/site-packages (from transformers) (1.26.4)\n", "Requirement already satisfied: packaging>=20.0 in /home/<USER>/.local/lib/python3.10/site-packages (from transformers) (24.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/lib/python3/dist-packages (from transformers) (5.4.1)\n", "Requirement already satisfied: regex!=2019.12.17 in /home/<USER>/.local/lib/python3.10/site-packages (from transformers) (2024.7.24)\n", "Requirement already satisfied: requests in /home/<USER>/.local/lib/python3.10/site-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: safetensors>=0.4.1 in /home/<USER>/.local/lib/python3.10/site-packages (from transformers) (0.4.3)\n", "Requirement already satisfied: tokenizers<0.20,>=0.19 in /home/<USER>/.local/lib/python3.10/site-packages (from transformers) (0.19.1)\n", "Requirement already satisfied: tqdm>=4.27 in /home/<USER>/.local/lib/python3.10/site-packages (from transformers) (4.66.4)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /home/<USER>/.local/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.23.2->transformers) (2024.5.0)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /home/<USER>/.local/lib/python3.10/site-packages (from huggingface-hub<1.0,>=0.23.2->transformers) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->transformers) (3.1.0)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/lib/python3/dist-packages (from requests->transformers) (3.3)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/lib/python3/dist-packages (from requests->transformers) (1.26.5)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/.local/lib/python3.10/site-packages (from requests->transformers) (2024.7.4)\n", "\u001b[33mWARNING: Error parsing dependencies of distro-info: Invalid version: '1.1build1'\u001b[0m\u001b[33m\n", "\u001b[0m\u001b[33mWARNING: Error parsing dependencies of python-debian: Invalid version: '0.1.43ubuntu1'\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["!python3 -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu\n", "!python3 -m pip install tokenizers -U\n", "!python3 -m pip install transformers -U"]}, {"cell_type": "code", "execution_count": 2, "id": "6a8a4bb6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import torch\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import jsonlines"]}, {"cell_type": "code", "execution_count": 6, "id": "ff804403", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Downloading shards: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████| 8/8 [05:11<00:00, 38.95s/it]\n", "Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████| 8/8 [00:01<00:00,  7.70it/s]\n"]}], "source": ["# Load the tokenizer and model from Hugging Face\n", "\n", "model_id = \"google/gemma-2-9b\"\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(model_id)\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    model_id,\n", "    torch_dtype=torch.float32,\n", ")"]}, {"cell_type": "markdown", "id": "9f218ba6", "metadata": {}, "source": ["## looping over multiple prompts and logits"]}, {"cell_type": "code", "execution_count": 7, "id": "c567f8d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data saved to golden_data_gemma2-9b.jsonl\n"]}], "source": ["# Save to disk\n", "output_path = \"golden_data_gemma2-9b.jsonl\"\n", "\n", "\n", "# Your prompt text\n", "prompt_texts = [\"I love to\", \"Today is a\", \"What is the\"]\n", "all_data_to_save = []\n", "\n", "\n", "for prompt_text in prompt_texts:\n", "  # Encode the prompt text\n", "  input_ids = tokenizer.encode(prompt_text, return_tensors=\"pt\")\n", "\n", "  # Get the logits for the prompt + completion\n", "  with torch.no_grad():\n", "    outputs = model(input_ids)\n", "    logits = outputs.logits\n", "\n", "    # Convert logits to fp32\n", "    logits = logits.cpu().numpy().astype(\"float32\")\n", "\n", "    # Prepare data to be saved\n", "    data_to_save = {\n", "        \"prompt\": prompt_text,\n", "        \"tokens\": input_ids.tolist()[0],\n", "        \"logits\": logits.tolist()[0],  # Convert numpy array to list for JSON serialization\n", "    }\n", "    all_data_to_save.append(data_to_save)\n", "\n", "with jsonlines.open(output_path, \"w\") as f:\n", "  f.write_all(all_data_to_save)\n", "\n", "\n", "print(f\"Data saved to {output_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}