{"cells": [{"cell_type": "code", "execution_count": null, "id": "4a921093", "metadata": {"scrolled": true}, "outputs": [], "source": "!python3 -m pip install -U \"jax[cpu]\""}, {"cell_type": "code", "execution_count": null, "id": "9e80b577", "metadata": {}, "outputs": [], "source": ["!git clone https://github.com/google-deepmind/gemma.git"]}, {"cell_type": "code", "execution_count": 1, "id": "be8907dd", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "VARIANT = \"2b\"  # @param ['2b', '2b-it', '7b', '7b-it'] {type:\"string\"}\n", "\n", "\n", "ckpt_path = \"/home/<USER>/workdir/gemma2-2b/ckpt/\"\n", "vocab_path = \"/home/<USER>/workdir/gemma2-2b/tokenizer.model\""]}, {"cell_type": "code", "execution_count": 2, "id": "cd6a2b85", "metadata": {}, "outputs": [], "source": ["# Load parameters\n", "from gemma.deprecated import params as params_lib\n", "\n", "params = params_lib.load_and_format_params(ckpt_path)"]}, {"cell_type": "code", "execution_count": 3, "id": "6908204c", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import sentencepiece as spm\n", "\n", "vocab = spm.SentencePieceProcessor()\n", "vocab.Load(vocab_path)"]}, {"cell_type": "code", "execution_count": 4, "id": "954b1e90", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["gemma2 2b\n"]}], "source": ["# We use the `transformer_lib.TransformerConfig.from_params` function to\n", "# automatically load the correct configuration from a checkpoint. Note that the\n", "# vocabulary size is smaller than the number of input embeddings due to unused\n", "# tokens in this release.\n", "\n", "from gemma.deprecated import transformer as transformer_lib\n", "\n", "config_2b = transformer_lib.TransformerConfig.from_params(\n", "    params, cache_size=30  # Number of time steps in the transformer's cache\n", ")\n", "model_2b = transformer_lib.Transformer(config=config_2b)"]}, {"cell_type": "code", "execution_count": 5, "id": "6d45d365", "metadata": {}, "outputs": [], "source": ["from gemma.deprecated import sampler as sampler_lib\n", "# Create a sampler with the right param shapes.\n", "sampler = sampler_lib.<PERSON>(\n", "    transformer=model_2b,\n", "    vocab=vocab,\n", "    params=params[\"transformer\"],\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "34ffb3ef", "metadata": {}, "outputs": [], "source": ["prompt_texts = [\"I love to\", \"Today is a\", \"What is the\"]\n", "# prompt_texts = [\"I love to\"]\n", "\n", "# out_data = sampler(\n", "#     input_strings=prompt_texts,\n", "#     total_generation_steps=10,  # number of steps performed when generating\n", "#   )\n", "\n", "# for input_string, out_string in zip(prompt_texts, out_data.text):\n", "#   print(f\"Prompt:\\n{input_string}\\nOutput:\\n{out_string}\")\n", "#   print()\n", "#   print(10*'#')"]}, {"cell_type": "code", "execution_count": 7, "id": "9b649f61", "metadata": {}, "outputs": [], "source": ["import jax\n", "\n", "\n", "def get_attention_mask_and_positions(\n", "    example: jax.<PERSON>,\n", "    pad_id: int,\n", ") -> tuple[jax.<PERSON>, jax.Array]:\n", "  \"\"\"Builds the position and attention mask vectors from the given tokens.\"\"\"\n", "\n", "  pad_mask = example != pad_id\n", "\n", "  current_token_position = transformer_lib.build_positions_from_mask(pad_mask)\n", "  attention_mask = transformer_lib.make_causal_attn_mask(pad_mask)\n", "  return current_token_position, attention_mask"]}, {"cell_type": "code", "execution_count": 8, "id": "647ea726", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["expanded_one_sample_input=Array([[     2, 235285,   2182,    577]], dtype=int32), positions=Array([0, 1, 2, 3], dtype=int32), attention_mask=Array([[[ True, False, False, False],\n", "        [ True,  True, False, False],\n", "        [ True,  True,  True, False],\n", "        [ True,  True,  True,  True]]], dtype=bool)\n", "embed output (Array(1, dtype=int32, weak_type=True), Array(4, dtype=int32, weak_type=True), Array(2304, dtype=int32, weak_type=True)), \n", "value [[[ 0.00604248 -0.28125     1.0859375  ...  0.703125   -0.3828125\n", "   -0.58984375]\n", "  [-0.94921875  1.0859375  -1.765625   ...  0.8359375  -1.546875\n", "    1.1640625 ]\n", "  [ 0.97265625 -3.25       -0.16894531 ... -0.34765625 -0.45703125\n", "    1.640625  ]\n", "  [ 0.19628906 -1.5234375  -2.671875   ...  0.20703125 -1.5625\n", "    1.9453125 ]]]\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "logits=Array([[[-2.42932873e+01, -8.72749424e+00, -6.97534180e+00, ...,\n", "         -2.36400928e+01, -2.37236576e+01, -2.36431370e+01],\n", "        [-1.70881042e+01, -1.62372553e+00,  4.96101379e-03, ...,\n", "         -9.01049423e+00, -9.51300812e+00, -9.94371796e+00],\n", "        [-2.09036140e+01, -1.08245525e+01,  5.70833588e+00, ...,\n", "         -1.42407427e+01, -1.43811245e+01, -1.42761621e+01],\n", "        [-2.44345226e+01, -7.46826744e+00, -1.59514780e+01, ...,\n", "         -1.85828953e+01, -1.91656723e+01, -1.90168114e+01]]],      dtype=float32)\n", "(1, 4, 256128)\n", "expanded_one_sample_input=Array([[    2, 15528,   603,   476]], dtype=int32), positions=Array([0, 1, 2, 3], dtype=int32), attention_mask=Array([[[ True, False, False, False],\n", "        [ True,  True, False, False],\n", "        [ True,  True,  True, False],\n", "        [ True,  True,  True,  True]]], dtype=bool)\n", "embed output (Array(1, dtype=int32, weak_type=True), Array(4, dtype=int32, weak_type=True), Array(2304, dtype=int32, weak_type=True)), \n", "value [[[ 6.0424805e-03 -2.8125000e-01  1.0859375e+00 ...  7.0312500e-01\n", "   -3.8281250e-01 -5.8984375e-01]\n", "  [ 2.0312500e+00 -3.0468750e+00  1.8920898e-03 ... -7.1093750e-01\n", "    2.0312500e+00  2.4687500e+00]\n", "  [-1.9453125e+00 -2.8710938e-01 -1.0546875e+00 ... -8.5546875e-01\n", "    1.3378906e-01  3.5625000e+00]\n", "  [-1.2968750e+00 -6.4062500e-01 -1.1718750e+00 ... -6.5234375e-01\n", "    1.3671875e-01  1.8750000e+00]]]\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "logits=Array([[[-24.293287 ,  -8.727494 ,  -6.975342 , ..., -23.640093 ,\n", "         -23.723658 , -23.643137 ],\n", "        [-18.786972 , -11.615761 ,   5.9547615, ..., -11.888696 ,\n", "         -12.40088  , -12.527592 ],\n", "        [-15.506657 ,  -4.366772 ,  -7.3351874, ..., -12.168879 ,\n", "         -12.821768 , -12.025688 ],\n", "        [-21.536617 ,  -4.9572086,  -6.252146 , ..., -15.954636 ,\n", "         -17.025518 , -16.357498 ]]], dtype=float32)\n", "(1, 4, 256128)\n", "expanded_one_sample_input=Array([[   2, 1841,  603,  573]], dtype=int32), positions=Array([0, 1, 2, 3], dtype=int32), attention_mask=Array([[[ True, False, False, False],\n", "        [ True,  True, False, False],\n", "        [ True,  True,  True, False],\n", "        [ True,  True,  True,  True]]], dtype=bool)\n", "embed output (Array(1, dtype=int32, weak_type=True), Array(4, dtype=int32, weak_type=True), Array(2304, dtype=int32, weak_type=True)), \n", "value [[[ 0.00604248 -0.28125     1.0859375  ...  0.703125   -0.3828125\n", "   -0.58984375]\n", "  [-1.640625    2.703125    0.29296875 ...  0.4140625   0.390625\n", "    1.        ]\n", "  [-1.9453125  -0.28710938 -1.0546875  ... -0.85546875  0.13378906\n", "    3.5625    ]\n", "  [-0.984375    1.1796875   0.08398438 ... -0.984375   -1.28125\n", "    1.203125  ]]]\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "logits=Array([[[-24.293287 ,  -8.727494 ,  -6.975342 , ..., -23.640093 ,\n", "         -23.723658 , -23.643137 ],\n", "        [-17.693571 ,  -1.1979847, -13.235206 , ..., -12.016174 ,\n", "         -11.018203 , -12.064774 ],\n", "        [-15.139654 ,  -1.7225208,  -1.8027275, ..., -12.853287 ,\n", "         -13.939721 , -12.8803625],\n", "        [-15.266034 , -15.230566 ,  -9.200054 , ..., -13.917203 ,\n", "         -14.561443 , -14.896409 ]]], dtype=float32)\n", "(1, 4, 256128)\n"]}], "source": ["import numpy as np\n", "import jax.numpy as jnp\n", "from gemma.deprecated import transformer as transformer_lib\n", "import jsonlines\n", "\n", "params = params_lib.load_and_format_params(ckpt_path)\n", "\n", "output_path = \"golden_data_gemma2-2b.jsonl\"\n", "all_data_to_save = []\n", "\n", "for prompt_index in range(len(prompt_texts)):\n", "  prompt_text = prompt_texts[prompt_index]\n", "  one_sample_input = np.array([2] + vocab.encode(prompt_text))\n", "  expanded_one_sample_input = jnp.expand_dims(one_sample_input, axis=0)\n", "  pad_id = vocab.pad_id\n", "  get_attention_mask_and_positions(one_sample_input, pad_id)\n", "  # Build the position and attention mask vectors.\n", "  positions, attention_mask = get_attention_mask_and_positions(one_sample_input, pad_id)\n", "  print(f\"{expanded_one_sample_input=}, {positions=}, {attention_mask=}\")\n", "\n", "  # Foward pass on the input data.\n", "  # No attention cache is needed here.\n", "\n", "  logits, _ = model_2b.apply(\n", "      #     params,\n", "      {\"params\": params[\"transformer\"]},\n", "      expanded_one_sample_input,\n", "      positions,\n", "      None,  # Attention cache is None.\n", "      attention_mask,\n", "  )\n", "  print(f\"{logits=}\")\n", "  print(logits.shape)\n", "  # Prepare data to be saved\n", "  data_to_save = {\n", "      \"prompt\": prompt_texts[prompt_index],\n", "      # \"completion\": out_data.text[prompt_index],\n", "      \"tokens\": [2] + vocab.encode(prompt_texts[prompt_index]),\n", "      \"logits\": logits[0].tolist(),  # remove the batch dim and then tolist() for json serialization\n", "  }\n", "  all_data_to_save.append(data_to_save)"]}, {"cell_type": "code", "execution_count": 9, "id": "53f4b01c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data saved to golden_data_gemma2-2b.jsonl\n"]}], "source": ["with jsonlines.open(output_path, \"w\") as f:\n", "  f.write_all(all_data_to_save)\n", "\n", "\n", "print(f\"Data saved to {output_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}