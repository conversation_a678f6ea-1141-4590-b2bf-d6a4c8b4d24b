{"cells": [{"cell_type": "code", "execution_count": null, "id": "4a921093", "metadata": {"scrolled": true}, "outputs": [], "source": "!python3 -m pip install -U \"jax[cpu]\""}, {"cell_type": "code", "execution_count": null, "id": "9e80b577", "metadata": {}, "outputs": [], "source": ["!git clone https://github.com/google-deepmind/gemma.git"]}, {"cell_type": "code", "execution_count": 1, "id": "be8907dd", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "VARIANT = \"27b\"  # @param ['2b', '2b-it', '7b', '7b-it'] {type:\"string\"}\n", "\n", "\n", "ckpt_path = \"/home/<USER>/data/gemma2/gemma2-27b/ckpt/\"\n", "vocab_path = \"/home/<USER>/data/gemma2/gemma2-27b/tokenizer.model\""]}, {"cell_type": "code", "execution_count": 2, "id": "cd6a2b85", "metadata": {}, "outputs": [], "source": ["# Load parameters\n", "from gemma.deprecated import params as params_lib\n", "\n", "params = params_lib.load_and_format_params(ckpt_path)"]}, {"cell_type": "code", "execution_count": 3, "id": "6908204c", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import sentencepiece as spm\n", "\n", "vocab = spm.SentencePieceProcessor()\n", "vocab.Load(vocab_path)"]}, {"cell_type": "code", "execution_count": 4, "id": "954b1e90", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["gemma2 27b\n"]}], "source": ["# We use the `transformer_lib.TransformerConfig.from_params` function to\n", "# automatically load the correct configuration from a checkpoint. Note that the\n", "# vocabulary size is smaller than the number of input embeddings due to unused\n", "# tokens in this release.\n", "\n", "from gemma.deprecated import transformer as transformer_lib\n", "\n", "config_27b = transformer_lib.TransformerConfig.from_params(\n", "    params, cache_size=30  # Number of time steps in the transformer's cache\n", ")\n", "model_27b = transformer_lib.Transformer(config=config_27b)"]}, {"cell_type": "code", "execution_count": 5, "id": "6d45d365", "metadata": {}, "outputs": [], "source": ["from gemma.deprecated import sampler as sampler_lib\n", "# Create a sampler with the right param shapes.\n", "sampler = sampler_lib.<PERSON>(\n", "    transformer=model_27b,\n", "    vocab=vocab,\n", "    params=params[\"transformer\"],\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "34ffb3ef", "metadata": {}, "outputs": [], "source": ["prompt_texts = [\"I love to\", \"Today is a\", \"What is the\"]\n", "# prompt_texts = [\"I love to\"]\n", "\n", "# out_data = sampler(\n", "#     input_strings=prompt_texts,\n", "#     total_generation_steps=10,  # number of steps performed when generating\n", "#   )\n", "\n", "# for input_string, out_string in zip(prompt_texts, out_data.text):\n", "#   print(f\"Prompt:\\n{input_string}\\nOutput:\\n{out_string}\")\n", "#   print()\n", "#   print(10*'#')"]}, {"cell_type": "code", "execution_count": 8, "id": "9b649f61", "metadata": {}, "outputs": [], "source": ["import jax\n", "\n", "\n", "def get_attention_mask_and_positions(\n", "    example: jax.<PERSON>,\n", "    pad_id: int,\n", ") -> tuple[jax.<PERSON>, jax.Array]:\n", "  \"\"\"Builds the position and attention mask vectors from the given tokens.\"\"\"\n", "\n", "  pad_mask = example != pad_id\n", "\n", "  current_token_position = transformer_lib.build_positions_from_mask(pad_mask)\n", "  attention_mask = transformer_lib.make_causal_attn_mask(pad_mask)\n", "  return current_token_position, attention_mask"]}, {"cell_type": "code", "execution_count": 9, "id": "647ea726", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["expanded_one_sample_input=Array([[     2, 235285,   2182,    577]], dtype=int32), positions=Array([0, 1, 2, 3], dtype=int32), attention_mask=Array([[[ True, False, False, False],\n", "        [ True,  True, False, False],\n", "        [ True,  True,  True, False],\n", "        [ True,  True,  True,  True]]], dtype=bool)\n", "embed output (Array(1, dtype=int32, weak_type=True), Array(4, dtype=int32, weak_type=True), Array(4608, dtype=int32, weak_type=True)), \n", "value [[[ 0.32421875 -0.36914062 -0.328125   ...  0.47070312 -0.34179688\n", "    0.26953125]\n", "  [-0.91015625  0.11523438 -0.5703125  ...  0.5859375  -0.03149414\n", "   -0.003479  ]\n", "  [ 0.14746094  0.67578125 -0.5625     ... -1.3515625  -0.13769531\n", "    1.3515625 ]\n", "  [-0.45703125  0.5234375  -0.07470703 ... -0.15527344 -0.05932617\n", "   -0.09619141]]]\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "logits=Array([[[  7.635116 ,  21.448622 ,  -0.50029  , ...,   7.501218 ,\n", "           7.490741 ,   7.638074 ],\n", "        [  2.9895227,   6.9226303,   3.0473156, ...,   2.8931296,\n", "           2.863878 ,   2.9946122],\n", "        [  2.6733346,  13.45678  , -12.752155 , ...,   2.6061206,\n", "           2.5570846,   2.7012296],\n", "        [ -4.6128597,  13.196595 , -16.251453 , ...,  -4.6230516,\n", "          -4.6622415,  -4.590245 ]]], dtype=float32)\n", "(1, 4, 256128)\n", "expanded_one_sample_input=Array([[    2, 15528,   603,   476]], dtype=int32), positions=Array([0, 1, 2, 3], dtype=int32), attention_mask=Array([[[ True, False, False, False],\n", "        [ True,  True, False, False],\n", "        [ True,  True,  True, False],\n", "        [ True,  True,  True,  True]]], dtype=bool)\n", "embed output (Array(1, dtype=int32, weak_type=True), Array(4, dtype=int32, weak_type=True), Array(4608, dtype=int32, weak_type=True)), \n", "value [[[ 0.32421875 -0.36914062 -0.328125   ...  0.47070312 -0.34179688\n", "    0.26953125]\n", "  [ 0.47070312 -1.1484375   0.21191406 ... -0.74609375  0.7265625\n", "   -0.77734375]\n", "  [-0.31835938  0.7578125  -0.22753906 ...  0.26171875  0.33203125\n", "    0.25195312]\n", "  [ 0.140625   -0.04858398 -0.7265625  ...  0.15039062  0.45117188\n", "   -0.33203125]]]\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "logits=Array([[[  7.635116 ,  21.448622 ,  -0.50029  , ...,   7.501218 ,\n", "           7.490741 ,   7.638074 ],\n", "        [ -1.6193094,  10.955722 ,  -3.5644789, ...,  -1.7052646,\n", "          -1.6816496,  -1.5931361],\n", "        [ -6.9146533,  12.466722 , -20.483839 , ...,  -7.02975  ,\n", "          -7.035371 ,  -6.8792124],\n", "        [ -5.7048645,  12.10754  , -27.359941 , ...,  -5.7306533,\n", "          -5.724037 ,  -5.6298766]]], dtype=float32)\n", "(1, 4, 256128)\n", "expanded_one_sample_input=Array([[   2, 1841,  603,  573]], dtype=int32), positions=Array([0, 1, 2, 3], dtype=int32), attention_mask=Array([[[ True, False, False, False],\n", "        [ True,  True, False, False],\n", "        [ True,  True,  True, False],\n", "        [ True,  True,  True,  True]]], dtype=bool)\n", "embed output (Array(1, dtype=int32, weak_type=True), Array(4, dtype=int32, weak_type=True), Array(4608, dtype=int32, weak_type=True)), \n", "value [[[ 0.32421875 -0.36914062 -0.328125   ...  0.47070312 -0.34179688\n", "    0.26953125]\n", "  [ 0.27734375  0.15820312  0.921875   ... -0.2890625  -0.30273438\n", "    0.9375    ]\n", "  [-0.31835938  0.7578125  -0.22753906 ...  0.26171875  0.33203125\n", "    0.25195312]\n", "  [-0.734375    0.04296875 -0.09472656 ...  0.8203125   0.52734375\n", "    0.11328125]]]\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "test dtype float32\n", "logits=Array([[[  7.635116  ,  21.448622  ,  -0.50029   , ...,   7.501218  ,\n", "           7.490741  ,   7.638074  ],\n", "        [ -1.0838276 ,   9.186724  ,  -3.5149524 , ...,  -1.1819512 ,\n", "          -1.1539855 ,  -1.101289  ],\n", "        [ -0.8186529 ,  13.241134  , -18.141693  , ...,  -0.90838504,\n", "          -0.9079076 ,  -0.7844023 ],\n", "        [ -1.9828255 ,  13.415507  , -23.958115  , ...,  -1.99186   ,\n", "          -1.9970742 ,  -1.9304956 ]]], dtype=float32)\n", "(1, 4, 256128)\n"]}], "source": ["import numpy as np\n", "import jax.numpy as jnp\n", "from gemma.deprecated import transformer as transformer_lib\n", "import jsonlines\n", "\n", "params = params_lib.load_and_format_params(ckpt_path)\n", "\n", "output_path = \"golden_data_gemma2-27b.jsonl\"\n", "all_data_to_save = []\n", "\n", "for prompt_index in range(len(prompt_texts)):\n", "  prompt_text = prompt_texts[prompt_index]\n", "  one_sample_input = np.array([2] + vocab.encode(prompt_text))\n", "  expanded_one_sample_input = jnp.expand_dims(one_sample_input, axis=0)\n", "  pad_id = vocab.pad_id\n", "  get_attention_mask_and_positions(one_sample_input, pad_id)\n", "  # Build the position and attention mask vectors.\n", "  positions, attention_mask = get_attention_mask_and_positions(one_sample_input, pad_id)\n", "  print(f\"{expanded_one_sample_input=}, {positions=}, {attention_mask=}\")\n", "\n", "  # Foward pass on the input data.\n", "  # No attention cache is needed here.\n", "\n", "  logits, _ = model_27b.apply(\n", "      #     params,\n", "      {\"params\": params[\"transformer\"]},\n", "      expanded_one_sample_input,\n", "      positions,\n", "      None,  # Attention cache is None.\n", "      attention_mask,\n", "  )\n", "  print(f\"{logits=}\")\n", "  print(logits.shape)\n", "  # Prepare data to be saved\n", "  data_to_save = {\n", "      \"prompt\": prompt_texts[prompt_index],\n", "      # \"completion\": out_data.text[prompt_index],\n", "      \"tokens\": [2] + vocab.encode(prompt_texts[prompt_index]),\n", "      \"logits\": logits[0].tolist(),  # remove the batch dim and then tolist() for json serialization\n", "  }\n", "  all_data_to_save.append(data_to_save)"]}, {"cell_type": "code", "execution_count": 10, "id": "53f4b01c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data saved to golden_data_gemma2-27b.jsonl\n"]}], "source": ["with jsonlines.open(output_path, \"w\") as f:\n", "  f.write_all(all_data_to_save)\n", "\n", "\n", "print(f\"Data saved to {output_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}