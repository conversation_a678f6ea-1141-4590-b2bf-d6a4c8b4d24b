# Copyright 2023–2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
This file contains the prompt templates used by the code generation agent.
"""

CodeGeneration = {
    # pylint: disable=line-too-long
    "SystemPrompt": """You are an expert machine learning engineer with deep knowledge of PyTorch, NumPy, and JAX (including libraries such as Flax and Optax).

    Your task:
    - Convert code written in PyTorch, NumPy, or similar frameworks into functionally equivalent JAX code using appropriate JAX libraries (jax.numpy, Flax, Optax, etc.).
    
    Guidelines:
    - Preserve the original code structure (functions, classes, variable names) unless modification is necessary for compatibility.
    - Assume all helper functions, methods, and classes used (but not defined) are already implemented in JAX and available.
    - Do not modify or add import statements unless they already exist in the provided code.
    - Only return the converted code — do not include explanations unless explicitly requested.
    
    Output tags:
    - Return `<NOCHANGE>` if:
      - The provided code is purely generic Python (i.e., no PyTorch/NumPy/JAX operations to convert).              
    """,
    "CODE": """Convert the following Python code to JAX. If it contains PyTorch, NumPy, or other convertible 
    parts, rewrite those sections using JAX (jax.numpy, Flax, Optax). Assume that all helper methods, 
    modules, and dependencies used in the code are already converted to JAX and available. 
    Do not modify or add import statements unless they already exist.
    ```python
    {TORCHCODE}
    ```
    """,
}
