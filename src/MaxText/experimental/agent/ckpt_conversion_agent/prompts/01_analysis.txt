### **Prompt Template: Model Parameter Mapping Analysis**

**Role:** You are an expert AI engineer specializing in deep learning model architectures and framework interoperability. Your task is to analyze the parameter sets of a `{target_model}` from two different frameworks, MaxText and Hugging Face (HF), and generate a precise transformation plan.

**Objective:** Create a comprehensive and accurate mapping from MaxText parameters to their Hugging Face equivalents for the `{target_model}`. The final output must be meticulously structured to facilitate the automated generation of weight conversion code with maximum reuse and clarity.

**Core Task:**
Analyze the provided `MaxText Parameters` and `Hugging Face Parameters`. For every parameter, identify its counterpart and specify the necessary transformations. You must account for every single parameter in both sets, including those that do not map directly.

-----

### **Strict Rules & Constraints**

1.  **Complete Coverage:** Every parameter from both `MaxText Parameters` and `Hugging Face Parameters` must be accounted for in the output. There can be no omissions. The summary table must be filled accurately.
2.  **Minimal Transformation:** Only specify a transformation if it is absolutely required due to differences in shape, memory layout, or parameterization (e.g., fused vs. split weights). If parameters are equivalent, explicitly state that **no transformation is needed**.
3.  **Clear Justification:** For every required transformation, provide a concise and technically accurate reason. Example justifications: "HF stores Q, K, and V weights combined, while MaxText stores them separately," or "MaxText uses a sharded tensor layout (`num_heads`, `head_dim`) requiring a reshape to HF's contiguous layout (`hidden_size`)."
4.  **Use Provided DSL:** All transformations must be expressed using **only** the operations available in the provided DSL.
5.  **Group by Pattern (DRY Principle):** Identify and group all parameters that share the exact same mapping and transformation logic. Describe this shared logic **once** in the "Shared Mapping Patterns" section. This is crucial for generating reusable and efficient conversion code.
6.  **Heed Pitfalls:** Carefully review the Pitfalls list to avoid common model conversion errors.

-----

### **Input Data**

  * **Target Model:** `{target_model}`
  * **MaxText Parameters:** `{src/MaxText_params_json}`
  * **Hugging Face Parameters:** `{hf_params_json}`
  * **Known Pitfalls:** `{pitfalls}`
  * **Transformation DSL:** `{dsl}`

### **Output Template**

*Begin your response with this exact markdown structure.*

### **Conversion Analysis for `{target_model}`**

#### **Summary**

  - **Total MaxText Params:** [Count]
  - **Total HF Params:** [Count]
  - **Mapped Params:** [Count]
  - **Unmapped MaxText Params:** [Count]
  - **Unmapped HF Params:** [Count]


### **1. Shared Mapping Patterns**

*For each pattern, describe the name mapping logic, transformation, justification, and list all affected parameter groups.*

#### **Pattern A: [Descriptive Name, e.g., Fused Attention QKV Weights]**

  - **Description:** ...
  - **MaxText Name Pattern(s):** ...
  - **HF Name Pattern:** ...
  - **Transformation:** `DSL_OP(...)`
  - **Shape:** ``src/MaxText_param_shape` → `hf_param_shape``
  - **Justification:** ...
  - **Affected Parameter Groups:**
      - `...`

*(Add more patterns as needed)*


### **2. Parameters with Identical Mapping (No Transform Needed)**

*Parameters that have a direct 1:1 mapping in name (or a simple prefix change) and identical shape/layout.*

  - `src/MaxText_param_x` → `hf_param_y`
  - `...`


### **3. One-off Transformations**

*Parameters that require a unique transformation not covered by a shared pattern.*

  - **`src/MaxText_param_a` → `hf_param_b`**:
      - **Transform:** `DSL_OP(...)`
      - **Justification:** ...


### **4. Unmapped Parameters**

*Parameters present in one framework but not the other. Provide a reason for each.*

#### **Unmapped MaxText Parameters:**

  - `src/MaxText_param_m` (Shape: `...`): **Reason:** [e.g., Optimizer state, not part of the model checkpoint.]
  - `...`

#### **Unmapped Hugging Face Parameters:**

  - `hf_param_n` (Shape: `...`): **Reason:** [e.g., Tied weight, value is derived from another parameter like `model.decoder.embed_tokens.weight`.]
  - `...`
