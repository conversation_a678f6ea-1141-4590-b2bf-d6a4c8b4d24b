* **Analysis:** {analysis}
* **Transformation DSL:** `{dsl}`
* **Feeeback from previous round:** `{feedback}`

You must generate a JSON object for EVERY parameter or parameter group mentioned in "Shared Mapping Patterns" and "One-off Transformations." Ignore Unmapped Parameters: Do not create JSON entries for any parameters listed in the "Unmapped Parameters" section.
Use Placeholders for Repeated Layers: For parameters that follow a pattern across numbered layers (e.g., layers.0.attn, layers.1.attn, etc.), you must create a single JSON entry using the placeholder {{i}} to represent the layer number.
Output Raw JSON Only: The final output must be only the raw JSON code. Do not add any explanations or markdown formatting in your final output.
Ultrathink
```JSON
[
  {{
    "src/MaxText": [
      "decoder.layers.0.self_attn.q_proj.weight",
    ],
    "huggingface": "model.layers.0.self_attn.q.weight",
    "op": DSL_OP(...)
  }}
]
```