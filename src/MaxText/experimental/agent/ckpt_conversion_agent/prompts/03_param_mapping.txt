Based on the following analysis, generate the Python code of the
`{target_model}_MAXTEXT_TO_HF_PARAM_MAPPING` function.

Analysis:
{analysis}

Hugging Face Parameters:
{hf_params_json}

MaxText Parameters:
{src/MaxText_params_json}

Pitfalls: 
{pitfalls}

Feedback:
{feedback}

Model Configurations:
#    - For text‐only models, use config directly, e.g: config.num_hidden_layers
#    - For multimodal, include both text_config & vision_config.
config:
  text_config?:
    num_hidden_layers: ${{config.text_config.num_hidden_layers}}
    hidden_size:        ${{config.text_config.hidden_size}}
    head_dim:           ${{config.text_config.head_dim}}
  vision_config?:
    num_hidden_layers: ${{config.vision_config.num_hidden_layers}}

Instructions:
1. Analyze the parameter names from {hf_params_json} and {src/MaxText_params_json}.
2. Create the base mapping for non-layer-specific weights (e.g., token embeddings).
#    Example: mapping[f"params-decoder-layers_1-pre_self_attention_norm_global-scale"]=
#                f"model.layers.1.input_layernorm.weight",
#
3. Implement the logic for `scan_layers`.
    - If `scan_layers` is True, loop through the number of layers and create a
      list of HF weight paths for each MaxText layer weight.
    - If `scan_layers` is False, create a direct 1-to-1 mapping for each layer.
4. Pay attention to potential architectural mismatches, like a single MaxText
    layer mapping to multiple HF layers (e.g., local and global attention).
5. Do not wrap the code in markdown backticks (no ` ```python` ).
6. The output must be only the raw Python code. Do not add any explanation.
7. Do not hard-code config numbers
8. Ensure that every src/MaxText and huggingface parameter name is covered in the output


Code Example:
def {target_model}_MAXTEXT_TO_HF_PARAM_MAPPING(config: Dict[str, Any], scan_layers: bool = False) -> Dict[str, Any]:
    \"\"\"
    Returns the mapping of parameter names from MaxText to Hugging Face for '{target_model}'.

    Args:
        config (dict): A model configuration dictionary containing hyperparameters
                       like 'num_hidden_layers'.
        scan_layers (bool): If True, the mapping for decoder layers will be a list
                            of HF paths corresponding to the stacked MaxText tensor.
                            If False, it will be a direct string-to-string mapping.

    Returns:
        A dictionary mapping MaxText parameter paths to their Hugging Face equivalents.
        The value can be a string (for 1-to-1 mapping) or a list of strings (for
        scanned layers).
    \"\"\"
    mapping = {{}}

    for i in range(config['num_hidden_layers']):
        if scan_layers:
            # Append to a list for each scanned weight
            pass
        else:
            # Create a direct string mapping
            pass

    return mapping