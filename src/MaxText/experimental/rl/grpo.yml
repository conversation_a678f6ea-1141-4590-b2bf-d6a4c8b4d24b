base_config: "base.yml"

use_grpo: True
train_data_columns: 'prompt'

learning_rate: 1.e-6

dataset_type: hf # we currently only support Huggingface input pipeline with GRPO.

#TRL
max_prefill_predict_length: 512
max_target_length: 1024

adam_b2: 0.999

# Group Relative Policy Optimization (GRPO)
num_generations: 4
grpo_beta: 0.04
inference_rollouts: 1
grpo_epsilon: 0.2

decode_sampling_strategy: "weighted"
decode_sampling_temperature: 0.9
async_checkpointing: false

# Pathways inference
inference_devices_per_replica: 4
inference_replicas: 1
use_pathways_reshard: True

return_log_prob: True 

add_bos: False
add_eos: False

### Splash attention block sizes
# These values are tuned for small sequence lengths used in the grpo test script.
sa_block_q: 128
sa_block_kv: 128
sa_block_kv_compute: 128
sa_block_q_dkv: 128
sa_block_kv_dkv: 128
sa_block_kv_dkv_compute: 128
sa_block_q_dq: 128
sa_block_kv_dq: 128
sa_use_fused_bwd_kernel: False
sa_q_layout: "HEAD_DIM_MINOR"
sa_k_layout: "HEAD_DIM_MINOR"
sa_v_layout: "HEAD_DIM_MINOR"
