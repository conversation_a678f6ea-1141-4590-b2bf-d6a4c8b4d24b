# NOTE: this is a preliminary recipe that enables multi-host inference on a v6e-64 GKE cluster
# using Jetstream on Pathways
# v6e-64
# tensor parallelism = 8, autoregressive parallelism = 8
# weight bf16, kv cache bf16

base_config: "inference_jetstream.yml"

sharding_strategy: "experimental"
attention: 'dot_product'
allow_split_physical_axes: True
# Used to replicate the quantization scale to avoid the inefficient XLA fusion.
replicate_quant_scale: True

logical_axis_rules: [
                      ['embed', []],
                      ['vocab', ['tensor', 'autoregressive']],
                      ['activation_batch', []],
                      ['activation_length', []],
                      ['activation_embed', []],
                      ['activation_vocab', ['tensor', 'autoregressive']],
                      ['heads', ['tensor']],
                      ['kv', []],
                      ['kv_heads', ['tensor']],
                      ['q_heads', ['tensor']],
                      ['kv_head_dim', []],
                      ['activation_prefill_kv_batch', []],
                      ['activation_kv_batch', ['autoregressive']],
                      ['activation_kv_heads', ['tensor']],
                      ['activation_kv_head_dim', []],
                      ['activation_heads', ['tensor']],
                      ['activation_kv', ['tensor', 'autoregressive']],
                      ['norm', []],
                      ['mlp', ['tensor', 'autoregressive']],
                      ['activation_mlp', ['tensor', 'autoregressive']],
                      ['cache_batch_prefill', []],
                      ['cache_batch', ['autoregressive']],
                      ['cache_sequence', []],
                      ['cache_heads', ['tensor']],
                      ['cache_kv', []],
                    ]

decoder_block: "llama4"
mlp_activations: ["silu","linear"]
enable_dropout: False
logits_via_embedding: False
tokenizer_type: "huggingface"
tokenizer_path: "meta-llama/Llama-4-Maverick-17B-128E"

base_emb_dim: 5120
base_num_decoder_layers: 48
base_num_query_heads: 40
base_num_kv_heads: 8
base_mlp_dim: 16384
base_moe_mlp_dim: 8192
vocab_size: 202048
normalization_layer_epsilon: 1e-05
rope_max_timescale: 500000
rope_type: "llama3.1"
rope_use_scale: False
num_experts: 128
capacity_factor: -1.0 # TODO: this will be removed once we support dropless with megablox/ragged_dot
shared_experts: 1
num_experts_per_tok: 1
use_qk_norm: False
nope_layer_interval: 4 # Every fourth layer should NOT use RoPE
interleave_moe_layer_step: 2 # Every 2nd layer is MoE layer, and 1st layer is dense layer

# TODO: delete the following variables once we add support for dropless with megablox/ragged_dot
sparse_matmul: False
megablox: False
