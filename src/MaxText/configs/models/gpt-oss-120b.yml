# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# model config for gpt-oss-120b
# https://huggingface.co/openai/gpt-oss-120b/blob/main/config.json

# tokenizer_type: "huggingface"

# Attention
base_emb_dim: 2880
base_num_query_heads: 64
base_num_kv_heads: 8
head_dim: 64
sliding_window_size: 128
attention_bias: True
attention_sink: True

# RoPE
rope_type: "yarn"
rope_max_timescale: 150_000
max_position_embeddings: 131072
original_max_position_embeddings: 4096
rope_factor: 32
beta_fast: 32
beta_slow: 1

# MLP
base_mlp_dim: 2880
base_moe_mlp_dim: 2880
mlp_activations: ["sigmoid","linear"]
mlp_activations_limit: 7.0
routed_bias: True
mlp_bias: True
num_experts: 128
num_experts_per_tok: 4

# General
base_num_decoder_layers: 36
vocab_size: 201088
normalization_layer_epsilon: 1.0e-5
enable_dropout: False
logits_via_embedding: False
decoder_block: "gpt_oss"
inhomogeneous_layer_cycle_interval: 2
