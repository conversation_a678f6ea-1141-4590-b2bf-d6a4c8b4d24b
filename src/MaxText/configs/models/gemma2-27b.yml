# Copyright 2023–2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# model config for gemma2-27B

base_emb_dim: 4608
base_num_query_heads: 32
base_num_kv_heads: 16
base_mlp_dim: 36864
base_num_decoder_layers: 23 # half of the real number of layers because we merge [local_attention, global_attention] into one layer 
head_dim: 128
mlp_activations: ["gelu","linear"]
vocab_size: 256128
decoder_block: "gemma2"
normalization_layer_epsilon: 1.e-06
logits_via_embedding: True
final_logits_soft_cap: 30.0
attn_logits_soft_cap: 50.0
sliding_window_size: 4096
use_post_attn_norm: True
use_post_ffw_norm: True
