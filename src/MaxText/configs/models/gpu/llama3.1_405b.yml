base_config: "base.yml"
run_name: "gpu_train_test"
# Args coming from the NVIDIA spreadsheet http://shortn/_AhULYn1mX4.
hardware: "gpu"
steps: 10
model_name: "llama3.1-405b"
enable_checkpointing: False
#attention: "cudnn_flash_te"
remat_policy: "full"
use_iota_embed: True
scan_layers: True
dataset_type: "synthetic"
async_checkpointing: False
logits_dot_in_fp32: False
per_device_batch_size: 1.0
max_target_length: 4096
