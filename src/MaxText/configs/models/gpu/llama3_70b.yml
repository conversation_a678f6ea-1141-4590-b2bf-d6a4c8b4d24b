# Copyright 2023–2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

base_config: "base.yml"
# The model_name guarantees we will use the correct model from
# configs/models/llama3-70b.yml, see update_model_vars in pyconfig.py for details.
model_name: "llama3-70b"

run_name: "gpu_train_test"
hardware: "gpu"
steps: 30
per_device_batch_size: 4
max_target_length: 8192
attention: "cudnn_flash_te"
remat_policy: "full"
use_iota_embed: True
dataset_type: "synthetic"
reuse_example_batch: 1
enable_checkpointing: False
