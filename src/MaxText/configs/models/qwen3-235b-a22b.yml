# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Model config for Qwen3-235B-A22B

# Core Architectural Parameters
decoder_block: "qwen3_moe"
base_emb_dim: 4096
base_num_query_heads: 64
base_num_kv_heads: 4
base_num_decoder_layers: 94
head_dim: 128
mlp_activations: ["silu", "linear"]
vocab_size: 151936
normalization_layer_epsilon: 1.0e-6
use_qk_norm: True

# MoE Specific Parameters
num_experts: 128
num_experts_per_tok: 8
base_moe_mlp_dim: 1536
load_balance_loss_weight: 0.001
norm_topk_prob: true

# RoPE Settings
rope_max_timescale: 5000000

# General Model Settings
enable_dropout: False
