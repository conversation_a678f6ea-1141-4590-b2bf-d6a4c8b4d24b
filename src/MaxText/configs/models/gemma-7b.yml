# Copyright 2023–2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# model config for gemma-7b

base_emb_dim: 3072
base_num_query_heads: 16
base_num_kv_heads: 16
base_mlp_dim: 24576
base_num_decoder_layers: 28
head_dim: 256
mlp_activations: ["gelu","linear"]
vocab_size: 256128
decoder_block: "gemma"
normalization_layer_epsilon: 1.e-06
logits_via_embedding: True