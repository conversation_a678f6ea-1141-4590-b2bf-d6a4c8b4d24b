# Copyright 2023–2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# model config for gemma3-4b

base_num_decoder_layers: 34
base_emb_dim: 2560
base_num_query_heads: 8
base_num_kv_heads: 4
base_mlp_dim: 10240
head_dim: 256
mlp_activations: ["gelu","linear"]
vocab_size: 262_144
decoder_block: "gemma3"
normalization_layer_epsilon: 1e-6
logits_via_embedding: True
sliding_window_size: 1024
use_post_attn_norm: true
use_post_ffw_norm: true
local_rope_max_timescale: 10_000
rope_max_timescale: 1_000_000
rope_linear_scaling_factor: 8.0
