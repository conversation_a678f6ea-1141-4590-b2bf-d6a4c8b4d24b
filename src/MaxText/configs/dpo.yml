base_config: "base.yml"

use_dpo: true
train_data_columns: ['chosen', 'rejected']
eval_data_columns: ['chosen', 'rejected']
base_output_directory: 'gs://src/MaxText-external/logs'

per_device_batch_size: 2.0
steps: 10
max_target_length: 512
eval_interval: 5  # test eval once, in the middle of 10 training steps
eval_steps: 2

# TFDS Pipeline ----------------------
dataset_type: tfds
dataset_path: 'gs://src/MaxText-dataset/dpo/anthropic_rlhf'
dataset_name: 'tfds:1.0.0'
eval_dataset_name: 'tfds:1.0.0'
eval_split: 'test'

# HF Pipeline -------------------------
hf_eval_split: 'test'

gradient_clipping_threshold: 10.0
learning_rate: 5.0e-7
dpo_label_smoothing: 0.0
dpo_beta: 0.1

enable_goodput_recording: false
monitor_goodput: false
enable_checkpointing: true
