# Copyright 2023–2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

base_config: "base.yml"

use_sft: True
# sft_train_on_completion_only=False trains on both prompt and completion tokens; trains only on completion tokens otherwise
sft_train_on_completion_only: True
packing: True
learning_rate: 2.e-5

# -------------- HF pipeline --------------
dataset_type: hf
hf_path: 'HuggingFaceH4/ultrachat_200k'
train_split: 'train_sft'
hf_eval_split: 'test_sft'
train_data_columns: ['messages']
eval_data_columns: ['messages']
