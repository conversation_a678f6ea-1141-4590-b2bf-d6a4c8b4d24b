{"cells": [{"cell_type": "code", "execution_count": null, "id": "bef36305", "metadata": {}, "outputs": [], "source": ["# Use nest_asyncio to allow nested event loops in notebooks\n", "!pip install nest_asyncio"]}, {"cell_type": "code", "execution_count": null, "id": "a8e986cb", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "\n", "import nest_asyncio\n", "\n", "# Get the current working directory of the notebook\n", "# This will be '.../src/MaxText/MaxText/scratch_code'\n", "current_dir = os.getcwd()\n", "\n", "# Navigate two levels up to get to the project root 'src/MaxText'\n", "project_root = os.path.abspath(os.path.join(current_dir, '..', '..'))\n", "\n", "# Add the project root to the system path if it's not already there\n", "if project_root not in sys.path:\n", "    sys.path.insert(0, project_root)\n", "    print(f\"Added '{project_root}' to sys.path\")\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "markdown", "id": "c4f53124", "metadata": {}, "source": ["Sanity test to ensure we have TPU devices accessible"]}, {"cell_type": "code", "execution_count": null, "id": "a545acd8", "metadata": {}, "outputs": [], "source": ["import jax\n", "jax.distributed.initialize() # distributed.initialize should only be called once.\n", "jax.devices()"]}, {"cell_type": "markdown", "id": "2836f120", "metadata": {}, "source": ["Import MaxText and its components"]}, {"cell_type": "code", "execution_count": null, "id": "0ab2e1dd", "metadata": {}, "outputs": [], "source": ["import MaxText as mt\n", "from MaxText import pyconfig\n", "from MaxText import src/MaxText_utils\n", "import numpy as np\n", "from MaxText.input_pipeline import _input_pipeline_utils\n", "import os\n", "from MaxText.globals import MAXTEXT_PKG_DIR\n", "from MaxText import max_logging\n", "from MaxText import common_types\n", "import jax\n", "from MaxText import inference_utils"]}, {"cell_type": "code", "execution_count": null, "id": "d2d2de93", "metadata": {}, "outputs": [], "source": ["# Replace path to your Llama3.1-8b checkpoint for the `load_parameters_path` argument.\n", "config = pyconfig.initialize(\n", "    [\"\", \"../configs/base.yml\"], \n", "    per_device_batch_size=1.0,\n", "    run_name=\"test\",\n", "    max_target_length=4,\n", "    max_prefill_predict_length=4,\n", "    tokenizer_type=\"tiktoken\",\n", "    tokenizer_path=\"assets/tokenizer_llama3.tiktoken/\",\n", "    load_parameters_path=\"path/to/your/llama3.1-8b/checkpoint\",  # Replace with your checkpoint path\n", "    model_name=\"llama3.1-8b\",\n", "    async_checkpointing=False,\n", "\n", ")\n", "\n", "model = mt.from_config(config)\n", "mesh = model.mesh\n", "init_rng = jax.random.PRNGKey(config.init_weights_seed)\n", "state, _ = src/MaxText_utils.setup_decode_state(model, config, init_rng, mesh, None)\n", "\n"]}, {"cell_type": "markdown", "id": "ed4b59a7", "metadata": {}, "source": ["Get Tokenizer"]}, {"cell_type": "code", "execution_count": null, "id": "249f8ef3", "metadata": {}, "outputs": [], "source": ["source_tokenizer = _input_pipeline_utils.get_tokenizer(\n", "        os.path.join(os.path.dirname(MAXTEXT_PKG_DIR), \"assets\", \"tokenizer_llama3.tiktoken\"),\n", "        \"tiktoken\",\n", "        add_bos=True,\n", "        add_eos=False,\n", "    )"]}, {"cell_type": "markdown", "id": "32a252ae", "metadata": {}, "source": ["Prepare the inputs"]}, {"cell_type": "code", "execution_count": null, "id": "d2d2d0c5", "metadata": {}, "outputs": [], "source": ["input_ids = source_tokenizer.encode(config.prompt)\n", "ids = np.asarray(input_ids, dtype=np.int32)\n", "s = (config.global_batch_size_to_train_on, config.max_target_length)\n", "decoder_segment_ids = np.zeros(s) + common_types.DECODING_ACTIVE_SEQUENCE_INDICATOR\n", "decoder_positions = np.stack(\n", "    [np.arange(config.max_target_length, dtype=np.int32) for _ in range(config.global_batch_size_to_train_on)]\n", ")\n", "\n", "\n", "ids = np.stack([ids for _ in range(config.global_batch_size_to_train_on)])\n", "max_logging.log(f\"input_ids={input_ids}, \\nids={ids}, \\ndecoder_segment_ids = {decoder_segment_ids}, \\ndecoder_positions= {decoder_positions}\")\n"]}, {"cell_type": "markdown", "id": "647018c1", "metadata": {}, "source": ["Run a forward pass"]}, {"cell_type": "code", "execution_count": null, "id": "7436751b", "metadata": {}, "outputs": [], "source": ["full_train_logits = model.apply(\n", "          state.params,\n", "          ids,\n", "          decoder_positions,\n", "          decoder_segment_ids,\n", "          enable_dropout=False,\n", "          rngs={\"aqt\": init_rng},\n", "      )\n", "full_train_logits = jax.experimental.multihost_utils.process_allgather(full_train_logits)\n", "max_logging.log(f\"{full_train_logits[0, 0, :]=}\")"]}, {"cell_type": "markdown", "id": "5640ab55", "metadata": {}, "source": ["Check the logits"]}, {"cell_type": "code", "execution_count": null, "id": "bb06c0c9", "metadata": {}, "outputs": [], "source": ["selected_logits = jax.lax.dynamic_slice(\n", "        full_train_logits,\n", "        (0, 0, full_train_logits.shape[2]-1, 0),\n", "        (1, 1, 1, full_train_logits.shape[3])\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "308f2a57", "metadata": {}, "outputs": [], "source": ["# Consider the greedily sampled token\n", "init_rng, new_rng = jax.random.split(init_rng)\n", "first_generated_token = inference_utils.sampling(\n", "        selected_logits,\n", "        new_rng,\n", "        config.decode_sampling_strategy, #\"greedy\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "32555a83", "metadata": {}, "outputs": [], "source": ["first_generated_token.item()"]}, {"cell_type": "code", "execution_count": null, "id": "3de52746", "metadata": {}, "outputs": [], "source": ["source_tokenizer.decode([first_generated_token.item()])"]}], "metadata": {"kernelspec": {"display_name": ".venv-py312", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}