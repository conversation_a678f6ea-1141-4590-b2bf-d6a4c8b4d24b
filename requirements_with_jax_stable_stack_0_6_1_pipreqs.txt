absl_py==2.2.2
aqt==25.2.7
benchmark_db_writer==1.0.0.dev20250610
benchmark_db_writer.egg==info
cloud_accelerator_diagnostics==0.1.1
cloud_tpu_diagnostics==0.1.5
datasets==3.6.0
etils==1.12.2
evaluate==0.4.4
flax==0.11.0
grain==0.2.10
grpcio==1.72.0rc1
huggingface_hub==0.33.0
jax==0.6.0
jaxlib==0.6.0 # Manually adding to ensure consistency in future
jaxtyping==0.3.2
jetstream==0.1.0
jsonlines==4.0.0
libtpu==0.0.15 # Manually adding to ensure consistency in future
matplotlib==3.10.3
ml_collections==1.1.0
ml_dtypes==0.5.1
ml_goodput_measurement==0.0.11
nltk==3.9.1
numpy==2.3.1
omegaconf==2.3.0
optax==0.2.5
orbax==0.11.22
pandas==2.3.0
pathwaysutils@git+https://github.com/AI-Hypercomputer/pathways-utils.git@d6fffd3f9bd5e06c323f5bade04b40fa741c728f
Pillow==11.2.1
protobuf==6.31.1
psutil==7.0.0
pytest==8.4.1
PyYAML==6.0.2
PyYAML==6.0.2
Requests==2.32.4
qwix@git+https://github.com/google/qwix.git
safetensors==0.5.3
sentencepiece==0.2.0
setuptools
tabulate==0.9.0
tensorboard_plugin_profile==2.13.0
tensorboardX==2.6.2.2
tensorboardX==2.6.4
tensorflow==2.19.0
tensorflow_datasets==4.9.9
tensorflow_text==2.19.0
tensorstore==0.1.75
tiktoken==0.9.0
torch==2.7.1
tqdm==4.67.1
transformer_engine==2.4.0
transformers==4.52.4
trl==0.19.0
urllib3==2.5.0
