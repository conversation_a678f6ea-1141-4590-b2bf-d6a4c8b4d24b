name: 'Close stale PRs'
on:
  schedule:
    - cron: '0 16 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      actions: write
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v9
        with:
          # Disable the action for issues to run on PRs only
          days-before-issue-stale: -1
          days-before-issue-close: -1
          # Number of days of inactivity before a PR is marked as stale
          days-before-pr-stale: 30
          # Number of days of inactivity before a stale PR is closed
          days-before-pr-close: 7
          # The label to apply to stale PRs
          stale-pr-label: 'stale'
          # This is true by default, adding just to be explicit
          remove-stale-when-updated: true
          # The comment to post when marking a PR as stale.
          stale-pr-message: >
            This PR has been automatically marked as stale because it has not had
            recent activity. It will be closed soon if no further activity occurs.
            Thank you for your contributions.
          # The comment to post when closing a stale PR.
          close-pr-message: >
            This PR was closed because it has been inactive for a while.
            Please reopen it if you are still working on it.

