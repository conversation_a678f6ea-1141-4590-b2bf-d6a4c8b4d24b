name: Linter

on:
  pull_request:

concurrency:
  # Dedup pull requests (canceling previous runs of the same workflow for same PR), and scheduled runs but nothing else
  group: >
    ${{
      github.event_name == 'pull_request' && format('{0}-pr-{1}', github.workflow, github.event.pull_request.number) ||
      github.event_name == 'schedule' && format('{0}-schedule', github.workflow) ||
      github.run_id
    }}
  cancel-in-progress: true

jobs:
  cpu:
    name: "CPU tests"
    runs-on: ubuntu-latest
    strategy:
      matrix:
        os: [ubuntu-24.04]
        python-version: ['3.12']
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install Dependencies
      run: |
        python3 -m pip install --upgrade pip
        python3 -m pip install pylint pyink pytype==2024.2.27
    - name: Typecheck the code with pytype
      run: |
        pytype --jobs auto --disable 'import-error,late-directive,wrong-arg-types,module-attr,unsupported-operands' src/MaxText/ || true
    - name: Analysing the code with pylint in Maxtext/
      run: |
         pylint --verbose --msg-template='[{abspath}] {msg_id}:{line:3d},{column}: {obj}: {msg}' --disable C0114,R0401,R0917,W0201,W0613 src/MaxText/ || true
    - name: Analysing the code with pylint in pedagogical_examples/
      run: |
         pylint pedagogical_examples/ && \
         echo 'PyLint check on pedagogical_examples/ is successful' || { echo \
         'PyLint check has failed. Please run bash code_style.sh to fix issues'; exit 20; }
    - name: Analysing the code with pyink in Maxtext/
      run: |
        pyink src/MaxText --check --diff --color --pyink-indentation=2 --line-length=125 || true
    - name: Analysing the code with pyink in pedagogical_examples/
      run: |
        pyink pedagogical_examples --check --diff --color --pyink-indentation=2 --line-length=125



