---
name: Bug Report
description: Report a bug or unexpected behavior.
labels:
- bug

body:
- type: markdown
  attributes:
    value: >
      Issue Type (please use labels): bug / feature request / documentation

      Please provide relevant information for your request. While all information is optional, providing details will help us correctly prioritize the issue.

      Note: if you’re working with a Google account team, please ALSO contact them directly to have an internal tracking bug created for prioritization.

- type: textarea
  attributes:
    label: Bug report
    description: >
      Steps to Reproduce: MaxText commands and/or step-by-step instructions to reproduce the problem. If possible, please include a minimal script or notebook that reproduces the issue. Please include any relevant configurations (e.g., flags, precision, sharding, etc).

      What did you expect to happen? What actually happened?
  validations:
    required: false

- type: textarea
  attributes:
    label: Logs/Output
    description: >
      Paste or attach relevant error messages, screenshots, stack traces, or logs.
  validations:
    required: false

- type: textarea
  attributes:
    label: Environment Information
    description: >
      MaxText version / git commit hash

      JAX version

      Python version

      Operating System (Linux, MacOS, etc)

      Hardware (e.g., Cloud Provider, TPU/GPU model, number of devices)

      Any special configuration (e.g., flags, precision, sharding strategy)
  validations:
    required: false

- type: textarea
  attributes:
    label: Additional Context
    description: >
      Which organization / company / university are you from ?
  validations:
    required: false
