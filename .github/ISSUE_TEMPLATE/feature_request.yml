---
name: Feature Request
description: Request a feature or model improvement.
labels:
- feature request

body:
- type: markdown
  attributes:
    value: >
      Issue Type (please use labels): bug / feature request / documentation

      Please provide relevant information for your request. While all information is optional, providing details will help us correctly prioritize the issue.

      Note: if you’re working with a Google account team, please ALSO contact them directly to have an internal tracking bug created for prioritization.

- type: textarea
  attributes:
    label: Feature or Model Request
    description: >
      To help us correctly prioritize the request, please include information about the requested feature, your use-case, and any other relevant info:

      What problem are you trying to solve? Why is this problem important ?

      Describe your requested feature or solution.

      Describe alternatives you’ve considered (if any).

      Additional context or examples.
  validations:
    required: false

- type: textarea
  attributes:
    label: Additional Context
    description: >
      Which organization / company / university are you from ?
  validations:
    required: false
