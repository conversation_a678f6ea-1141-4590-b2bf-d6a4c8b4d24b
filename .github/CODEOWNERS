* @gobbleturk @khatwan<PERSON><PERSON> @bvandermoon @vipannalla @RissyRan @richjames0 @gagika @shralex @yangyuwei @SurbhiJainUSC @hengtaoguo @A9isha @aireenmei @NuojCheng

# Features
src/MaxText/experimental/rl @A9isha @khatwanimohit @gagika @richjames0
src/MaxText/input_pipeline @aireenmei @SurbhiJainUSC @richjames0
src/MaxText/kernels/megablox @RissyRan @michelle-yooh @gagika @richjames0
src/MaxText/kernels/ragged_attention.py @patemotter @vipannalla @richjames0
src/MaxText/layers/pipeline.py @gobbleturk @richjames0
src/MaxText/layers/moe.py @RissyRan @michelle-yooh @gagika @richjames0
src/MaxText/layers/multi_token_prediction.py @parambole @RissyRan @gagika @richjames0
src/MaxText/elastic_train.py @luke<PERSON><PERSON> @shaury<PERSON><PERSON> @RoshaniN
src/MaxText/layers/quantizations.py @khatwanimohit @jshin1394 @liudangyi @richjames0

# Inference
src/MaxText/tests/inference @vipannalla @mitalisi @gpolovets1 @mailvijayasingh @jrplatin @patemotter @lumosis @richjames0
src/MaxText/inference @vipannalla @mitalisi @gpolovets1 @mailvijayasingh @jrplatin @patemotter @lumosis @richjames0
src/MaxText/inference_mlperf @vipannalla @mitalisi @gpolovets1 @mailvijayasingh @jrplatin @patemotter @lumosis @richjames0

# Dockerfiles and dependencies
*.Dockerfile @bvandermoon @yangyuwei @parambole @richjames0
*.txt @bvandermoon @yangyuwei @parambole @richjames0

# Workflow files
.github/workflows @gobbleturk @khatwanimohit @shralex @parambole @richjames0

# Benchmarking/Recipes
benchmarks @SujeethJinesh @bvandermoon @richjames0 @shralex @vipannalla @mitalisi @RissyRan @shauryagup
