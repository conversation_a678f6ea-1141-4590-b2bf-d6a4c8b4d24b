<!--
 Copyright 2024 Google LLC

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
 -->

# Terminology

- **FLOP**: Floating Point Operation
- **FLOPS**: Plural form of FLOP
- **FLOP/s** or **FLOPs**: stands for Floating Point Operations Per Second.
- **MFU**: Model FLOP/s Utilization
- **ICI**: Interchip-interconnect.
- **HBM**: High Bandwidth Memory. Built with DRAM technology. Each chip usually has XX GiBs of HBM.
- **VMEM**: Vector Memory. Built with SRAM technology. Each chip usually has XX MiBs of VMEM.
- **DCN**: Data Center Network 
- **PCIe**: Peripheral Component Interconnect Express. How the TPUs communicate with the CPU.
- **AI**: Arithmetic Intensity
- **Rank**: Position or ID of a worker within a group of workers. This is not the same as Rank in linear algebra.
