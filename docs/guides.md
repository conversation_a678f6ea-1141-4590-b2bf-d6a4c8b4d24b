<!--
 Copyright 2024 Google LLC

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
 -->

# How-to guides

```{toctree}
:maxdepth: 1

guides/checkpoints.md
guides/custom_model.md
guides/run_src/MaxText_via_xpk.md
guides/run_src/MaxText_via_pathways.md
guides/data_input_pipeline.md
guides/single_host_gpu.md
guides/knowledge_distillation.md
guides/gcp_workload_observability.md
guides/monitor_goodput.md
guides/use_vertex_ai_tensorboard.md
guides/features_and_diagnostics.md
guides/performance_metrics.md
guides/understand_logs_and_metrics.md
guides/checkpointing_solutions/gcs_checkpointing.md
guides/checkpointing_solutions/emergency_checkpointing.md
guides/checkpointing_solutions/multi_tier_checkpointing.md
```
