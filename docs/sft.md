<!--
 Copyright 2023–2025 Google LLC

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
 -->

# Try SFT!
Supervised fine-tuning (SFT) is a process where a pre-trained large language model is fine-tuned on a labeled dataset to adapt the model to perform better on specific tasks.

This tutorial demonstrates step-by-step instructions for setting up the environment and then training the Llama3.1 8B model on the [HuggingFaceH4/ultrachat_200k](https://huggingface.co/datasets/HuggingFaceH4/ultrachat_200k) dataset using SFT. If you wish to use a different dataset, you can [update the dataset configurations](https://github.com/AI-Hypercomputer/src/MaxText/blob/main/MaxText/configs/sft.yml).

We use [Tunix](https://github.com/google/tunix), a JAX-based library designed for post-training tasks, to perform SFT.
 
In this tutorial we use a single host TPU VM such as `v6e-8/v5p-8`. Let's get started!

## Setup virtual environment

### Create a Python3.12 virtual environment
```
bash setup.sh
```

### Activate virtual environment
```
# Replace with your virtual environment name if not using this default name
venv_name="src/MaxText_venv"
source ~/$venv_name/bin/activate
```

### Install MaxText dependencies
```
bash setup.sh
```

## Run SFT
There are two scenarios supported for running SFT:
1. **Run SFT on Hugging Face checkpoint**  
    Download the checkpoint directly from Hugging Face and fine-tune it using SFT.

2. **Run SFT on MaxText checkpoint**  
    Use a checkpoint generated by MaxText and fine-tune it using SFT.

Choose the scenario that matches your workflow and follow the corresponding instructions below.

### Run SFT on Hugging Face checkpoint
* The script will first convert a Hugging Face checkpoint to a MaxText checkpoint.
* It then runs SFT on this converted checkpoint.
* After fine-tuning, the script converts the resulting checkpoint back to the Hugging Face format.

#### Setup environment variables
```
 export HF_TOKEN=<Hugging Face access token>

 export BASE_OUTPUT_DIRECTORY=<output directory to store run logs>

 export STEPS=<number of fine-tuning steps to run>

 export PER_DEVICE_BATCH_SIZE=1
```

Finally, run the [script](https://github.com/AI-Hypercomputer/src/MaxText/blob/main/end_to_end/tpu/llama3.1/8b/run_sft.sh):
```
bash ~/src/MaxText/end_to_end/tpu/llama3.1/8b/run_sft.sh
```

### Run SFT on MaxText checkpoint
* The script directly runs SFT on MaxText checkpoint.
* After fine-tuning, the script converts the resulting checkpoint back to the Hugging Face format.

#### Setup environment variables
```
 export HF_TOKEN=<Hugging Face access token>

 export BASE_OUTPUT_DIRECTORY=<output directory to store run logs>

 export STEPS=<number of fine-tuning steps to run>

 export PER_DEVICE_BATCH_SIZE=1

 export PRE_TRAINED_MODEL_CKPT_PATH=<gcs path for MaxText checkpoint>
```

Finally, run the [script](https://github.com/AI-Hypercomputer/src/MaxText/blob/main/end_to_end/tpu/llama3.1/8b/run_sft.sh):
```
bash ~/src/MaxText/end_to_end/tpu/llama3.1/8b/run_sft.sh
```
