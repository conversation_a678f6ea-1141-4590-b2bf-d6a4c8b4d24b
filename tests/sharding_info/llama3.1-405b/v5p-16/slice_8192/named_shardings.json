{".step": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": []}, ".params/['params']/['decoder']/['decoder_norm']/['scale']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence"]]}, ".params/['params']/['decoder']/['layers']/['mlp']/['wi_0']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "sequence", "tensor_transpose", "context", "expert"], "stage", ["fsdp_transpose", "tensor", "tensor_sequence", "autoregressive"]]}, ".params/['params']/['decoder']/['layers']/['mlp']/['wi_1']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "sequence", "tensor_transpose", "context", "expert"], "stage", ["fsdp_transpose", "tensor", "tensor_sequence", "autoregressive"]]}, ".params/['params']/['decoder']/['layers']/['mlp']/['wo']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp_transpose", "tensor", "tensor_sequence", "autoregressive"], "stage", ["fsdp", "sequence", "tensor_transpose", "context", "expert"]]}, ".params/['params']/['decoder']/['layers']/['post_self_attention_layer_norm']/['scale']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence"], "stage"]}, ".params/['params']/['decoder']/['layers']/['pre_self_attention_layer_norm']/['scale']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence"], "stage"]}, ".params/['params']/['decoder']/['layers']/['self_attention']/['key']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], "stage", ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], null]}, ".params/['params']/['decoder']/['layers']/['self_attention']/['out']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], "stage", null, ["fsdp", "fsdp_transpose", "sequence", "context", "expert"]]}, ".params/['params']/['decoder']/['layers']/['self_attention']/['query']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], "stage", ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], null]}, ".params/['params']/['decoder']/['layers']/['self_attention']/['value']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], "stage", ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], null]}, ".params/['params']/['decoder']/['logits_dense']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"]]}, ".params/['params']/['token_embedder']/['embedding']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], ["fsdp", "fsdp_transpose", "sequence", "context", "expert"]]}, ".opt_state/[0]/.count": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": []}, ".opt_state/[0]/.mu/['params']/['decoder']/['decoder_norm']/['scale']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence"]]}, ".opt_state/[0]/.mu/['params']/['decoder']/['layers']/['mlp']/['wi_0']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "sequence", "tensor_transpose", "context", "expert"], "stage", ["fsdp_transpose", "tensor", "tensor_sequence", "autoregressive"]]}, ".opt_state/[0]/.mu/['params']/['decoder']/['layers']/['mlp']/['wi_1']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "sequence", "tensor_transpose", "context", "expert"], "stage", ["fsdp_transpose", "tensor", "tensor_sequence", "autoregressive"]]}, ".opt_state/[0]/.mu/['params']/['decoder']/['layers']/['mlp']/['wo']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp_transpose", "tensor", "tensor_sequence", "autoregressive"], "stage", ["fsdp", "sequence", "tensor_transpose", "context", "expert"]]}, ".opt_state/[0]/.mu/['params']/['decoder']/['layers']/['post_self_attention_layer_norm']/['scale']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence"], "stage"]}, ".opt_state/[0]/.mu/['params']/['decoder']/['layers']/['pre_self_attention_layer_norm']/['scale']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence"], "stage"]}, ".opt_state/[0]/.mu/['params']/['decoder']/['layers']/['self_attention']/['key']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], "stage", ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], null]}, ".opt_state/[0]/.mu/['params']/['decoder']/['layers']/['self_attention']/['out']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], "stage", null, ["fsdp", "fsdp_transpose", "sequence", "context", "expert"]]}, ".opt_state/[0]/.mu/['params']/['decoder']/['layers']/['self_attention']/['query']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], "stage", ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], null]}, ".opt_state/[0]/.mu/['params']/['decoder']/['layers']/['self_attention']/['value']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], "stage", ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], null]}, ".opt_state/[0]/.mu/['params']/['decoder']/['logits_dense']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"]]}, ".opt_state/[0]/.mu/['params']/['token_embedder']/['embedding']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], ["fsdp", "fsdp_transpose", "sequence", "context", "expert"]]}, ".opt_state/[0]/.nu/['params']/['decoder']/['decoder_norm']/['scale']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence"]]}, ".opt_state/[0]/.nu/['params']/['decoder']/['layers']/['mlp']/['wi_0']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "sequence", "tensor_transpose", "context", "expert"], "stage", ["fsdp_transpose", "tensor", "tensor_sequence", "autoregressive"]]}, ".opt_state/[0]/.nu/['params']/['decoder']/['layers']/['mlp']/['wi_1']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "sequence", "tensor_transpose", "context", "expert"], "stage", ["fsdp_transpose", "tensor", "tensor_sequence", "autoregressive"]]}, ".opt_state/[0]/.nu/['params']/['decoder']/['layers']/['mlp']/['wo']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp_transpose", "tensor", "tensor_sequence", "autoregressive"], "stage", ["fsdp", "sequence", "tensor_transpose", "context", "expert"]]}, ".opt_state/[0]/.nu/['params']/['decoder']/['layers']/['post_self_attention_layer_norm']/['scale']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence"], "stage"]}, ".opt_state/[0]/.nu/['params']/['decoder']/['layers']/['pre_self_attention_layer_norm']/['scale']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence"], "stage"]}, ".opt_state/[0]/.nu/['params']/['decoder']/['layers']/['self_attention']/['key']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], "stage", ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], null]}, ".opt_state/[0]/.nu/['params']/['decoder']/['layers']/['self_attention']/['out']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], "stage", null, ["fsdp", "fsdp_transpose", "sequence", "context", "expert"]]}, ".opt_state/[0]/.nu/['params']/['decoder']/['layers']/['self_attention']/['query']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], "stage", ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], null]}, ".opt_state/[0]/.nu/['params']/['decoder']/['layers']/['self_attention']/['value']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], "stage", ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], null]}, ".opt_state/[0]/.nu/['params']/['decoder']/['logits_dense']/['kernel']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["fsdp", "fsdp_transpose", "sequence", "context", "expert"], ["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"]]}, ".opt_state/[0]/.nu/['params']/['token_embedder']/['embedding']": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": [["tensor", "tensor_transpose", "tensor_sequence", "autoregressive"], ["fsdp", "fsdp_transpose", "sequence", "context", "expert"]]}, ".opt_state/[2]/.count": {"mesh": {"axis_names": ["data", "stage", "fsdp", "fsdp_transpose", "sequence", "context", "context_autoregressive", "tensor", "tensor_transpose", "tensor_sequence", "expert", "autoregressive"], "shape": {"data": 8192, "stage": 1, "fsdp": 8, "fsdp_transpose": 1, "sequence": 1, "context": 1, "context_autoregressive": 1, "tensor": 1, "tensor_transpose": 1, "tensor_sequence": 1, "expert": 1, "autoregressive": 1}}, "partition_spec": []}}