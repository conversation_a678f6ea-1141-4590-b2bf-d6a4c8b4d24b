[build-system]
requires = ["hatchling", "hatch-requirements-txt"]
build-backend = "hatchling.build"

[tool.hatch.version]
path = "src/MaxText/__init__.py"

[project]
name = "MaxText"
description = "MaxText is a simple, performant and scalable Jax LLM!"
dynamic = ["dependencies", "optional-dependencies", "version"]
requires-python = ">=3.10"
readme = "README.md"
license = "Apache-2.0"
keywords = ["llm", "jax", "llama", "mistral", "mixtral", "gemma", "deepseek"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: ML",
    "Environment :: GPU"
]

[project.urls]
Repository = "https://github.com/AI-Hypercomputer/src/MaxText.git"
"Bug Tracker" = "https://github.com/AI-Hypercomputer/src/MaxText/issues"

#[tool.hatch.metadata.hooks.requirements_txt]
#files = ["requirements.txt"]

[tool.hatch.metadata]
allow-direct-references = true

#[tool.hatch.metadata.hooks.requirements_txt.optional-dependencies]
#gpu = ["requirements-gpu.txt"]
#tpu = ["requirements-tpu.txt"]

[tool.hatch.build.targets.wheel]
packages = ["src/MaxText"]
