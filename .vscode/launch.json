{"version": "0.2.0", "configurations": [{"name": "Debug MaxText Decode (llama2-7b-int8)", "type": "python", "request": "launch", "console": "integratedTerminal", "justMyCode": false, "python": "python3", "module": "MaxText.decode", "args": ["src/MaxText/configs/base.yml", "run_name=runner_$(date +%Y-%m-%d-%H-%M)", "base_output_directory=gs://test-src/MaxText-output", "dataset_path=gs://test-src/MaxText-dataset", "model_name=llama2-7b", "load_parameters_path=gs://msingh-bkt/checkpoints/quant_llama2-7b-chat/20241120034012/int8_", "tokenizer_path=assets/tokenizer.llama2", "per_device_batch_size=8", "max_prefill_predict_length=8", "max_target_length=20", "weight_dtype=bfloat16", "ici_fsdp_parallelism=1", "ici_tensor_parallelism=-1", "scan_layers=false", "quantization=int8", "checkpoint_is_quantized=true", "attention=dot_product", "autoregressive_decode_assert=travel and explore new places"]}, {"name": "Debug MaxText Decode (Test)", "type": "python", "request": "launch", "console": "integratedTerminal", "justMyCode": false, "python": "python3", "module": "MaxText.decode", "args": ["src/MaxText/configs/base.yml", "run_name=runner_$(date +%Y-%m-%d-%H-%M)", "base_output_directory=gs://test-src/MaxText-output", "dataset_path=gs://test-src/MaxText-dataset", "steps=2", "attention=dot_product", "enable_checkpointing=false"]}, {"name": "Debug MaxText Train", "type": "python", "request": "launch", "console": "integratedTerminal", "justMyCode": false, "python": "python3", "module": "MaxText.train", "args": ["src/MaxText/configs/base.yml", "run_name=runner_$(date +%Y-%m-%d-%H-%M)", "base_output_directory=gs://test-src/MaxText-output", "dataset_path=gs://test-src/MaxText-dataset", "steps=2", "enable_checkpointing=false"]}, {"name": "Debug MaxText Inference Microbenchmark", "type": "python", "request": "launch", "console": "integratedTerminal", "justMyCode": false, "python": "python3", "module": "MaxText.inference_microbenchmark", "args": ["src/MaxText/configs/base.yml", "model_name=llama2-7b", "tokenizer_path=assets/tokenizer.llama2", "weight_dtype=bfloat16", "scan_layers=false", "attention=dot_product", "max_prefill_predict_length=1024", "max_target_length=2048", "ici_fsdp_parallelism=1", "ici_tensor_parallelism=-1", "ici_autoregressive_parallelism=1", "inference_microbenchmark_prefill_lengths=32,64,128,256,512,1024", "inference_microbenchmark_stages=generate", "inference_microbenchmark_loop_iters=1", "run_name=runner_$(date +%Y-%m-%d-%H-%M)", "base_output_directory=gs://test-src/MaxText-output", "prefill_cache_axis_order=0,2,1,3", "ar_cache_axis_order=0,2,1,3", "compute_axis_order=0,2,1,3", "reshape_q=true", "per_device_batch_size=24", "quantization=int8", "quantize_kvcache=True"]}]}