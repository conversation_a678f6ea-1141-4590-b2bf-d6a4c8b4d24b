Deprecated>=1.2.18
Jinja2>=3.1.6
Markdown>=3.7
MarkupSafe>=3.0.2
PyGObject>=3.42.1
PyYAML>=5.4.1
Pygments==2.18.0
Werkzeug==2.0.3
absl-py==2.1.0
aiohappyeyeballs==2.4.3
aiohttp==3.10.10
aiosignal==1.3.1
annotated-types==0.7.0
anyio==4.6.2.post1
aqtp==0.8.2
array_record==0.7.2
astroid==3.3.5
astunparse==1.6.3
async-timeout==4.0.3
attrs==24.2.0
black==24.8.0
blobfile==3.0.0
build==1.2.2.post1
cachetools==5.5.0
certifi==2024.8.30
cfgv==3.4.0
charset-normalizer==3.4.0
chex==0.1.87
click==8.1.7
cloud-accelerator-diagnostics==0.1.1
cloud-tpu-diagnostics==0.1.5
cloudpickle==3.1.0
clu==0.0.12
contextlib2==21.6.0
coverage==7.6.4
datasets==3.0.2
dbus-python==1.2.18
decorator==5.1.1
dill==0.3.8
distlib==0.3.9
dm-tree==0.1.8
docstring_parser==0.16
editdistance==0.8.1
etils==1.10.0
exceptiongroup==1.2.2
fastapi==0.115.3
filelock==3.16.1
flatbuffers==24.3.25
flax==0.11.0
frozenlist==1.5.0
fsspec==2024.9.0
gast==0.6.0
gcsfs==2024.9.0.post1
google-api-core==2.24.1
google-auth-oauthlib==1.2.1
google-auth==2.35.0
google-cloud-aiplatform==1.61.0
google-cloud-appengine-logging==1.4.5
google-cloud-audit-log==0.3.0
google-cloud-bigquery==3.26.0
google-cloud-core==2.4.1
google-cloud-logging==3.11.3
google-cloud-resource-manager==1.12.5
google-cloud-storage==2.18.2
google-crc32c==1.6.0
google-pasta==0.2.0
google-resumable-media==2.7.2
googleapis-common-protos==1.65.0
grain-nightly==0.0.10
grpc-google-iam-v1==0.13.1
grpcio-status==1.48.2
grpcio==1.67.0
gviz-api==1.10.0
h11==0.14.0
h5py==3.12.1
huggingface-hub==0.26.1
humanize==4.11.0
identify==2.6.1
idna==3.10
immutabledict==4.2.0
importlab==0.8.1
importlib_metadata==8.4.0
importlib_resources==6.4.5
iniconfig==2.0.0
isort
jax-cuda12-pjrt
jax-cuda12-plugin
jax==0.6.2
jaxlib==0.6.2
jaxtyping==0.3.2
jsonlines==4.0.0
keras==3.10.0
libclang==18.1.1
libcst==1.8.2
lxml==5.4.0
markdown-it-py==3.0.0
mccabe==0.7.0
mdurl==0.1.2
ml-collections==0.1.1
ml-dtypes==0.4.1
ml-goodput-measurement==0.0.10
more-itertools==10.5.0
msgpack==1.1.0
msgspec==0.18.6
multidict==6.1.0
multiprocess==0.70.16
mypy-extensions==1.0.0
namex==0.0.8
nest-asyncio==1.6.0
networkx==3.4.2
ninja==********
nodeenv==1.9.1
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvcc-cu12==12.6.85
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==********
nvidia-nccl-cu12==2.23.4
nvidia-nvjitlink-cu12==12.6.85
oauthlib==3.2.2
omegaconf
opentelemetry-api==1.27.0
opt_einsum==3.4.0
optax==0.2.3
optree==0.13.0
orbax-checkpoint==0.11.22
packaging==24.1
pandas==2.2.3
parameterized==0.9.0
pathspec==0.12.1
pip-tools==7.4.1
platformdirs==4.3.6
pluggy==1.5.0
portpicker==1.6.0
pre_commit==4.0.1
prometheus_client==0.21.0
promise==2.3
propcache==0.2.0
proto-plus==1.25.0
protobuf==3.20.3
psutil==6.1.0
pyarrow==17.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pybind11==2.13.6
pybind11_global==2.13.6
pycnite==2024.7.31
pycryptodomex==3.21.0
pydantic==2.9.2
pydantic_core==2.23.4
pydot==3.0.2
pyglove==0.4.4
pyink==24.10.0
pylint==3.3.1
pyparsing==3.2.0
pyproject_hooks==1.2.0
pytest==8.3.3
python-dateutil==2.9.0.post0
pytype==2024.10.11
pytz==2024.2
qwix@git+https://github.com/google/qwix.git
regex==2024.9.11
requests-oauthlib==2.0.0
requests==2.32.3
rich==13.9.3
rsa==4.9
safetensors==0.4.5
scipy==1.14.1
sentencepiece==0.1.97
seqio==0.0.19
shapely==2.0.6
shortuuid==1.0.13
simple-parsing==0.1.6
six==1.16.0
sniffio==1.3.1
starlette==0.41.0
tabulate==0.9.0
tensorboard-data-server==0.7.2
tensorboard-plugin-profile==2.17.0
tensorboard==2.17.1
tensorboardX==*******
tensorflow-datasets==4.9.6
tensorflow-io-gcs-filesystem==0.37.1
tensorflow-metadata==1.16.1
tensorflow-text==2.17.0
tensorflow==2.17.0
tensorstore==0.1.68
termcolor==2.5.0
tfds-nightly==4.9.2.dev202308090034
tiktoken==0.8.0
tokenizers==0.20.1
toml==0.10.2
tomli==2.0.2
tomlkit==0.13.2
toolz==1.0.0
tqdm==4.66.5
transformer-engine==1.13.0+e5edd6c
transformers==4.46.0
tunix@git+https://github.com/google/tunix.git
typeguard==2.13.3
typing_extensions==4.12.2
tzdata==2024.2
urllib3==2.2.3
uvicorn==0.32.0
virtualenv==20.27.0
wrapt==1.16.0
xxhash==3.5.0
yarl==1.16.0
zipp==3.20.2
