# pytest.ini
[pytest]
testpaths =
    tests
python_files = *_test.py *_tests.py
addopts =
    -rf --import-mode=importlib --strict-markers
    --ignore=tests/profiler_test.py
    --ignore=tests/train_smoke_test.py
    --ignore=tests/train_int8_smoke_test.py
    --ignore=tests/train_gpu_smoke_test.py
    --ignore=tests/train_using_ragged_dot_smoke_test.py
    --ignore=tests/grpo_trainer_correctness_test.py
    --ignore=tests/offline_engine_test.py
markers =
    tpu_only: marks tests to be run on TPUs only
    gpu_only: marks tests to be run on GPUs only
    cpu_only: marks tests to be run on CPUs only
    scheduled_only: marks tests to run only on scheduled CI runs
    integration_test: tests exercising larger portions of the system,
                      including interactions with other systems like GCS,
                      e.g., end_to_end tests
